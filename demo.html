<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Search Engine Demo - ToolBrothers Style</title>
    <link rel="stylesheet" href="extensions/search-engine/assets/search.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        
        .demo-header {
            background: white;
            padding: 20px 0;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 30px;
        }
        
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-search-wrapper {
            max-width: 500px;
            margin: 0 auto 40px;
            position: relative;
        }
        
        .demo-search-input {
            width: 100%;
            padding: 14px 50px 14px 18px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            font-family: inherit;
            background: white;
            outline: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .demo-search-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        .demo-search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            padding: 10px;
            cursor: pointer;
            color: #666;
            transition: color 0.3s ease;
        }
        
        .demo-search-btn:hover {
            color: #007bff;
        }
        
        .demo-autocomplete {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
        }
        
        .demo-autocomplete.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <div class="demo-container">
            <h1 class="demo-title">Shopify Search Engine Demo</h1>
            <p style="text-align: center; color: #666; margin-bottom: 0;">ToolBrothers Style Design</p>
        </div>
    </div>

    <div class="demo-container">
        <!-- Search Bar Demo -->
        <div class="demo-search-wrapper">
            <input 
                type="text" 
                class="demo-search-input" 
                placeholder="Search for tools, equipment, parts..."
                id="demoSearch"
                autocomplete="off"
            >
            <button class="demo-search-btn" onclick="performDemoSearch()">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
            </button>
            
            <!-- Demo Autocomplete -->
            <div class="demo-autocomplete" id="demoAutocomplete">
                <div class="autocomplete-header">
                    Search suggestions for 'makita'
                </div>
                <div class="autocomplete-suggestions">
                    <div class="autocomplete-suggestion">makita</div>
                    <div class="autocomplete-suggestion">makita tools</div>
                    <div class="autocomplete-suggestion">makita drill</div>
                    <div class="autocomplete-suggestion">makita battery</div>
                    <div class="autocomplete-suggestion">makita saw</div>
                </div>
                <div class="autocomplete-products">
                    <div class="autocomplete-product" onclick="alert('Going to Makita Drill product page')">
                        <img src="https://via.placeholder.com/50x50/007bff/ffffff?text=M" alt="Makita Drill" class="autocomplete-product-image">
                        <div class="autocomplete-product-info">
                            <div class="autocomplete-product-title">Makita 18V LXT Cordless Drill</div>
                            <div class="autocomplete-product-meta">Makita • SKU: DHP484Z</div>
                            <div class="autocomplete-product-price">$149.99</div>
                        </div>
                    </div>
                    <div class="autocomplete-product" onclick="alert('Going to Makita Saw product page')">
                        <img src="https://via.placeholder.com/50x50/28a745/ffffff?text=M" alt="Makita Saw" class="autocomplete-product-image">
                        <div class="autocomplete-product-info">
                            <div class="autocomplete-product-title">Makita Circular Saw 7-1/4"</div>
                            <div class="autocomplete-product-meta">Makita • SKU: 5007MG</div>
                            <div class="autocomplete-product-price">$199.99</div>
                        </div>
                    </div>
                    <div class="autocomplete-product" onclick="alert('Going to Makita Impact Driver product page')">
                        <img src="https://via.placeholder.com/50x50/dc3545/ffffff?text=M" alt="Makita Impact" class="autocomplete-product-image">
                        <div class="autocomplete-product-info">
                            <div class="autocomplete-product-title">Makita 18V Impact Driver</div>
                            <div class="autocomplete-product-meta">Makita • SKU: DTD153Z</div>
                            <div class="autocomplete-product-price">$129.99</div>
                        </div>
                    </div>
                    <div class="autocomplete-product" onclick="alert('Going to Makita Grinder product page')">
                        <img src="https://via.placeholder.com/50x50/6f42c1/ffffff?text=M" alt="Makita Grinder" class="autocomplete-product-image">
                        <div class="autocomplete-product-info">
                            <div class="autocomplete-product-title">Makita 4-1/2" Angle Grinder</div>
                            <div class="autocomplete-product-meta">Makita • SKU: 9557PBG</div>
                            <div class="autocomplete-product-price">$89.99</div>
                        </div>
                    </div>
                </div>
                <div class="autocomplete-show-all">
                    <a href="#" onclick="alert('Would show full search results page'); return false;">Show all results</a>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div style="max-width: 800px; margin: 0 auto; text-align: center; color: #666;">
            <h2 style="color: #333; margin-bottom: 20px;">ToolBrothers Style Search</h2>
            <p style="margin-bottom: 15px;">
                <strong>Type "makita" in the search box above</strong> to see the dropdown with suggestions and product results.
            </p>
            <p style="margin-bottom: 15px;">
                • <strong>Search suggestions</strong> appear at the top (like "makita tools", "makita drill")
            </p>
            <p style="margin-bottom: 15px;">
                • <strong>Product results</strong> appear below with images, titles, and prices
            </p>
            <p style="margin-bottom: 15px;">
                • <strong>Click any product</strong> to go directly to the product page
            </p>
            <p style="color: #999; font-size: 14px;">
                This matches the ToolBrothers interface where everything happens in the dropdown modal.
            </p>
        </div>
    </div>

    <script>
        // Demo functionality
        function performDemoSearch() {
            const input = document.getElementById('demoSearch');
            const autocomplete = document.getElementById('demoAutocomplete');
            
            if (input.value.toLowerCase().includes('makita')) {
                autocomplete.classList.add('show');
            } else {
                autocomplete.classList.remove('show');
            }
        }

        // Show autocomplete on focus/input
        document.getElementById('demoSearch').addEventListener('input', function(e) {
            const autocomplete = document.getElementById('demoAutocomplete');
            if (e.target.value.length >= 2) {
                autocomplete.classList.add('show');
            } else {
                autocomplete.classList.remove('show');
            }
        });

        // Hide autocomplete when clicking outside
        document.addEventListener('click', function(e) {
            const searchWrapper = document.querySelector('.demo-search-wrapper');
            const autocomplete = document.getElementById('demoAutocomplete');
            
            if (!searchWrapper.contains(e.target)) {
                autocomplete.classList.remove('show');
            }
        });
    </script>
</body>
</html>
