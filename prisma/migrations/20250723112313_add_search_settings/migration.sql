-- CreateTable
CREATE TABLE "SearchSettings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "fastApiProxyUrl" TEXT NOT NULL DEFAULT 'http://localhost:8000',
    "fastApi<PERSON><PERSON><PERSON><PERSON>" TEXT,
    "websiteKey" TEXT NOT NULL,
    "elasticsearchIndex" TEXT NOT NULL DEFAULT 'shopify_products',
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "SearchSettings_shop_key" ON "SearchSettings"("shop");

-- CreateIndex
CREATE UNIQUE INDEX "SearchSettings_websiteKey_key" ON "SearchSettings"("websiteKey");
