#!/usr/bin/env node

/**
 * Database setup script for the Shopify Search Engine app
 * This script initializes the SQLite database and runs migrations
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up database for Shopify Search Engine...\n');

// Check if Prisma is available
try {
  execSync('npx prisma --version', { stdio: 'pipe' });
} catch (error) {
  console.error('❌ Prisma CLI not found. Please run "npm install" first.');
  process.exit(1);
}

// Generate Prisma client
console.log('📦 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully\n');
} catch (error) {
  console.error('❌ Failed to generate Prisma client');
  process.exit(1);
}

// Check if database exists
const dbPath = path.join(__dirname, '..', 'dev.sqlite');
const dbExists = fs.existsSync(dbPath);

if (!dbExists) {
  console.log('🗄️  Creating new SQLite database...');
  try {
    execSync('npx prisma db push', { stdio: 'inherit' });
    console.log('✅ Database created successfully\n');
  } catch (error) {
    console.error('❌ Failed to create database');
    process.exit(1);
  }
} else {
  console.log('🗄️  Database already exists, running migrations...');
  try {
    execSync('npx prisma db push', { stdio: 'inherit' });
    console.log('✅ Database updated successfully\n');
  } catch (error) {
    console.error('❌ Failed to update database');
    process.exit(1);
  }
}

console.log('🎉 Database setup complete!');
console.log('\nNext steps:');
console.log('1. Copy .env.example to .env and configure your settings');
console.log('2. Run "npm run dev" to start the development server');
console.log('3. Install the app in your Shopify store');
console.log('4. Configure your FastAPI proxy settings in the app admin\n');
