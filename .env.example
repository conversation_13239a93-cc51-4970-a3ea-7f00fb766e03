# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key_here
SHOPIFY_API_SECRET=your_shopify_api_secret_here
SCOPES=read_products
HOST=https://your-app-domain.com

# Session Storage (SQLite for simplicity)
DATABASE_URL=file:./dev.sqlite
SHOPIFY_APP_URL=https://your-app-domain.com

# Default FastAPI Proxy Configuration (can be overridden in admin settings)
FASTAPI_PROXY_URL=http://localhost:8000
FASTAPI_API_KEY=
WEBSITE_KEY=shopify_products
ELASTICSEARCH_INDEX_NAME=shopify_products

# Development
NODE_ENV=development
