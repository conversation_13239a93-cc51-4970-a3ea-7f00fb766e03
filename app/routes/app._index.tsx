import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Badge,
  InlineStack,
  Box,
  Divider,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getSearchSettings } from "../services/settings.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const settings = await getSearchSettings(session.shop);
  
  return json({ 
    settings,
    shopDomain: session.shop 
  });
};

export default function Index() {
  const { settings } = useLoaderData<typeof loader>();

  return (
    <Page>
      <TitleBar title="Search Engine Dashboard" />
      <Layout>
        {/* Main Content Section */}
        <Layout.Section>
          <BlockStack gap="500">
            {/* Hero Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <InlineStack align="space-between" blockAlign="center">
                  <BlockStack gap="100">
                    <Text as="h2" variant="headingLg">
                      Advanced Search Engine
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Powered by FastAPI and Elasticsearch
                    </Text>
                  </BlockStack>
                  <Badge size="large" tone={settings.isEnabled ? "success" : "critical"}>
                    {settings.isEnabled ? "Active" : "Inactive"}
                  </Badge>
                </InlineStack>
                
                <Divider />
                
                <Text variant="bodyMd" as="p">
                  Enhance your store's search functionality with our FastAPI-powered 
                  Elasticsearch integration. Provide customers with fast, accurate 
                  product search results.
                </Text>
                
                <InlineStack gap="300" wrap={false}>
                  <Button variant="primary" url="/app/settings">
                    Configure Settings
                  </Button>
                  <Button url="/app/analytics" variant="secondary">
                    View Analytics
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>

            {/* Configuration Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  Configuration Overview
                </Text>
                
                <Box paddingBlockStart="100">
                  <BlockStack gap="300">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd" as="span" fontWeight="medium">
                        FastAPI Proxy URL
                      </Text>
                      <Text variant="bodyMd" as="span" tone="subdued">
                        {settings.fastApiProxyUrl || "Not configured"}
                      </Text>
                    </InlineStack>
                    
                    <InlineStack align="space-between">
                      <Text variant="bodyMd" as="span" fontWeight="medium">
                        Elasticsearch Index
                      </Text>
                      <Text variant="bodyMd" as="span" tone="subdued">
                        {settings.websiteKey || "Not configured"}
                      </Text>
                    </InlineStack>
                    
                    <InlineStack align="space-between">
                      <Text variant="bodyMd" as="span" fontWeight="medium">
                        Last Updated
                      </Text>
                      <Text variant="bodyMd" as="span" tone="subdued">
                        {settings.updatedAt 
                          ? new Date(settings.updatedAt).toLocaleString() 
                          : "Never"}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        {/* Sidebar Section */}
        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            {/* Status Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  System Status
                </Text>
                
                <BlockStack gap="300">
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="span">
                      Search Engine
                    </Text>
                    <Badge tone={settings.isEnabled ? "success" : "critical"}>
                      {settings.isEnabled ? "Operational" : "Disabled"}
                    </Badge>
                  </InlineStack>
                  
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="span">
                      API Connection
                    </Text>
                    <Badge tone={settings.fastApiProxyUrl ? "success" : "critical"}>
                      {settings.fastApiProxyUrl ? "Connected" : "Disconnected"}
                    </Badge>
                  </InlineStack>
                  
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="span">
                      Index Status
                    </Text>
                    <Badge tone={settings.websiteKey ? "success" : "warning"}>
                      {settings.websiteKey ? "Active" : "Pending"}
                    </Badge>
                  </InlineStack>
                </BlockStack>
              </BlockStack>
            </Card>

            {/* Quick Actions Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">
                  Quick Actions
                </Text>
                
                <BlockStack gap="200">
                  <Button url="/app/settings" fullWidth>
                    Manage Settings
                  </Button>
                  <Button url="/app/reindex" fullWidth variant="secondary">
                    Rebuild Index
                  </Button>
                  <Button url="/app/docs" fullWidth variant="tertiary">
                    Documentation
                  </Button>
                </BlockStack>
              </BlockStack>
            </Card>

            {/* Need Help Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="300">
                <Text as="h2" variant="headingSm">
                  Need Help?
                </Text>
                <Text as="p" variant="bodySm">
                  Visit our documentation or contact support for assistance with 
                  your search engine configuration.
                </Text>
                <InlineStack gap="200">
                  <Button url="/app/docs" variant="plain">
                    Documentation
                  </Button>
                  <Button url="/app/support" variant="plain">
                    Contact Support
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}