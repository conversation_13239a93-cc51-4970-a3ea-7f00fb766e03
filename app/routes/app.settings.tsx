import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useActionData, useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  TextField,
  Button,
  FormLayout,
  Checkbox,
  Banner,
  Text,
  BlockStack,
  InlineStack,
  Badge,
  Divider,
  Link, // Added Link import
  Box, // Added Box import
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";
import {
  getSearchSettings,
  updateSearchSettings,
  testFastApiConnection,
  testElasticsearchIndex,
  validateSearchSettings,
  type UpdateSearchSettingsData,
  type SearchSettings,
} from "../services/settings.server";

// Define action data types
type ActionData =
  | { success: true; message: string; settings?: SearchSettings }
  | { success: false; errors: string[] }
  | { success: false; message: string }
  | { success: boolean; connectionTest: { success: boolean; error?: string; responseTime?: number } }
  | { success: boolean; indexTest: { success: boolean; documentCount?: number; error?: string } };

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const settings = await getSearchSettings(session.shop);
  return json({ settings });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    if (action === "update") {
      const updateData: UpdateSearchSettingsData = {
        fastApiProxyUrl: formData.get("fastApiProxyUrl") as string,
        fastApiApiKey: (formData.get("fastApiApiKey") as string) || undefined,
        websiteKey: formData.get("websiteKey") as string,
        elasticsearchIndex: formData.get("elasticsearchIndex") as string,
        isEnabled: formData.get("isEnabled") === "true",
      };

      // Validate the data
      const validation = validateSearchSettings(updateData);
      if (!validation.isValid) {
        return json({
          success: false,
          errors: validation.errors,
        });
      }

      const updatedSettings = await updateSearchSettings(session.shop, updateData);
      return json({
        success: true,
        message: "Settings updated successfully",
        settings: updatedSettings,
      });
    }

    if (action === "test-connection") {
      const fastApiProxyUrl = formData.get("fastApiProxyUrl") as string;
      const fastApiApiKey = formData.get("fastApiApiKey") as string || null;
      const result = await testFastApiConnection(fastApiProxyUrl, fastApiApiKey);
      return json({
        success: result.success,
        connectionTest: result,
        message: result.success
          ? `Connection successful (${result.responseTime}ms)`
          : `Connection failed: ${result.error}`,
      });
    }

    if (action === "test-index") {
      const fastApiProxyUrl = formData.get("fastApiProxyUrl") as string;
      const fastApiApiKey = formData.get("fastApiApiKey") as string || null;
      const websiteKey = formData.get("websiteKey") as string;
      const elasticsearchIndex = formData.get("elasticsearchIndex") as string;
      const result = await testElasticsearchIndex(
        fastApiProxyUrl,
        fastApiApiKey,
        websiteKey,
        elasticsearchIndex
      );
      return json({
        success: result.success,
        indexTest: result,
        message: result.success
          ? `Index accessible (${result.documentCount} documents)`
          : `Index test failed: ${result.error}`,
      });
    }

    return json({ success: false, message: "Invalid action" });
  } catch (error) {
    console.error("Settings action error:", error);
    return json({
      success: false,
      message: error instanceof Error ? error.message : "An error occurred",
    });
  }
};

export default function SettingsPage() {
  const { settings } = useLoaderData<typeof loader>();
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const submit = useSubmit();

  // Form state
  const [formData, setFormData] = useState({
    fastApiProxyUrl: settings.fastApiProxyUrl,
    fastApiApiKey: settings.fastApiApiKey || "",
    websiteKey: settings.websiteKey,
    elasticsearchIndex: settings.elasticsearchIndex,
    isEnabled: settings.isEnabled,
  });

  const isLoading = navigation.state === "submitting";
  const isTestingConnection = navigation.formData?.get("action") === "test-connection";
  const isTestingIndex = navigation.formData?.get("action") === "test-index";

  // Update form data when settings change
  useEffect(() => {
    if (actionData && 'settings' in actionData && actionData.settings) {
      setFormData({
        fastApiProxyUrl: actionData.settings.fastApiProxyUrl,
        fastApiApiKey: actionData.settings.fastApiApiKey || "",
        websiteKey: actionData.settings.websiteKey,
        elasticsearchIndex: actionData.settings.elasticsearchIndex,
        isEnabled: actionData.settings.isEnabled,
      });
    }
  }, [actionData]);

  const handleFieldChange = useCallback((field: string) => (value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSave = useCallback(() => {
    const data = new FormData();
    data.append("action", "update");
    data.append("fastApiProxyUrl", formData.fastApiProxyUrl);
    data.append("fastApiApiKey", formData.fastApiApiKey);
    data.append("websiteKey", formData.websiteKey);
    data.append("elasticsearchIndex", formData.elasticsearchIndex);
    data.append("isEnabled", formData.isEnabled.toString());
    submit(data, { method: "post" });
  }, [formData, submit]);

  const handleTestConnection = useCallback(() => {
    const data = new FormData();
    data.append("action", "test-connection");
    data.append("fastApiProxyUrl", formData.fastApiProxyUrl);
    data.append("fastApiApiKey", formData.fastApiApiKey);
    submit(data, { method: "post" });
  }, [formData, submit]);

  const handleTestIndex = useCallback(() => {
    const data = new FormData();
    data.append("action", "test-index");
    data.append("fastApiProxyUrl", formData.fastApiProxyUrl);
    data.append("fastApiApiKey", formData.fastApiApiKey);
    data.append("websiteKey", formData.websiteKey);
    data.append("elasticsearchIndex", formData.elasticsearchIndex);
    submit(data, { method: "post" });
  }, [formData, submit]);

  // Helper to render test results
  const renderTestResult = (testResult: any, isConnectionTest: boolean) => {
    if (!testResult) return null;

    if (isConnectionTest) {
      return (
        <InlineStack gap="200" align="end">
          <Badge tone={testResult.success ? "success" : "critical"}>
            {testResult.success ? "Connected" : "Failed"}
          </Badge>
          {testResult.success && testResult.responseTime !== undefined && (
            <Text as="span" variant="bodySm" tone="subdued">
              ({testResult.responseTime}ms)
            </Text>
          )}
        </InlineStack>
      );
    } else {
      return (
        <InlineStack gap="200" align="end">
          <Badge tone={testResult.success ? "success" : "critical"}>
            {testResult.success ? "Accessible" : "Failed"}
          </Badge>
          {testResult.success && testResult.documentCount !== undefined && (
            <Text as="span" variant="bodySm" tone="subdued">
              ({testResult.documentCount} documents)
            </Text>
          )}
        </InlineStack>
      );
    }
  };

  return (
    <Page
      title="Search Engine Settings"
      subtitle="Configure your FastAPI proxy and Elasticsearch integration"
      primaryAction={{
        content: "Save Settings",
        onAction: handleSave,
        loading: isLoading && !isTestingConnection && !isTestingIndex,
      }}
    >
      <Layout>
        {/* Alerts Section */}
        <Layout.Section>
          <BlockStack gap="400">
            {actionData && !actionData.success && 'errors' in actionData && actionData.errors && (
              <Banner tone="critical" title="Validation Errors">
                <ul>
                  {actionData.errors.map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </Banner>
            )}
            {actionData && actionData.success && 'message' in actionData && actionData.message && (
              <Banner tone="success" title="Success" onDismiss={() => {}}>
                <p>{actionData.message}</p>
              </Banner>
            )}
            {actionData && !actionData.success && 'message' in actionData && actionData.message && !('errors' in actionData) && (
              <Banner tone="critical" title="Error">
                <p>{actionData.message}</p>
              </Banner>
            )}
          </BlockStack>
        </Layout.Section>

        {/* Main Configuration Section */}
        <Layout.Section>
          <BlockStack gap="500">
            {/* FastAPI Configuration Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text variant="headingLg" as="h2">
                  FastAPI Proxy Configuration
                </Text>
                <FormLayout>
                  <TextField
                    label="FastAPI Proxy URL"
                    value={formData.fastApiProxyUrl}
                    onChange={handleFieldChange("fastApiProxyUrl")}
                    placeholder="https://your-fastapi-proxy.com"
                    helpText="The base URL of your FastAPI proxy service"
                    autoComplete="off"
                  />
                  <TextField
                    label="API Key (Optional)"
                    value={formData.fastApiApiKey}
                    onChange={handleFieldChange("fastApiApiKey")}
                    placeholder="Enter your API key"
                    helpText="Authentication key for your FastAPI proxy"
                    type="password"
                    autoComplete="off"
                    monospaced
                  />
                </FormLayout>
                <Box>
                  <InlineStack align="space-between" blockAlign="center">
                    <Button
                      onClick={handleTestConnection}
                      loading={isTestingConnection}
                      disabled={!formData.fastApiProxyUrl}
                    >
                      Test Connection
                    </Button>
                    {actionData && 'connectionTest' in actionData && renderTestResult(actionData.connectionTest, true)}
                  </InlineStack>
                </Box>
              </BlockStack>
            </Card>

            {/* Search Index Configuration Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text variant="headingLg" as="h2">
                  Search Index Configuration
                </Text>
                <FormLayout>
                  <TextField
                    label="Website Key"
                    value={formData.websiteKey}
                    onChange={handleFieldChange("websiteKey")}
                    placeholder="my-shopify-store"
                    helpText="Unique identifier for your website in the search system"
                    autoComplete="off"
                  />
                  <TextField
                    label="Elasticsearch Index Name"
                    value={formData.elasticsearchIndex}
                    onChange={handleFieldChange("elasticsearchIndex")}
                    placeholder="shopify_products"
                    helpText="Name of the Elasticsearch index to search"
                    autoComplete="off"
                  />
                </FormLayout>
                <Box>
                  <InlineStack align="space-between" blockAlign="center">
                    <Button
                      onClick={handleTestIndex}
                      loading={isTestingIndex}
                      disabled={!formData.fastApiProxyUrl || !formData.websiteKey || !formData.elasticsearchIndex}
                    >
                      Test Index Access
                    </Button>
                    {actionData && 'indexTest' in actionData && renderTestResult(actionData.indexTest, false)}
                  </InlineStack>
                </Box>
              </BlockStack>
            </Card>

            {/* Activation Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text variant="headingLg" as="h2">
                  Activation
                </Text>
                <Checkbox
                  label="Enable Search Engine"
                  checked={formData.isEnabled}
                  onChange={handleFieldChange("isEnabled")}
                  helpText="Toggle to enable or disable the search functionality on your storefront"
                />
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        {/* Sidebar Section */}
        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            {/* Status Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  Current Status
                </Text>
                <BlockStack gap="300">
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="span" fontWeight="medium">
                      Search Engine
                    </Text>
                    <Badge tone={settings.isEnabled ? "success" : "critical"} size="small">
                      {settings.isEnabled ? "Enabled" : "Disabled"}
                    </Badge>
                  </InlineStack>
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="span" fontWeight="medium">
                      Last Updated
                    </Text>
                    <Text variant="bodyMd" as="span" tone="subdued">
                      {new Date(settings.updatedAt).toLocaleString()}
                    </Text>
                  </InlineStack>
                </BlockStack>
              </BlockStack>
            </Card>

            {/* Help Card */}
            <Card roundedAbove="sm">
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  Help & Documentation
                </Text>
                <BlockStack gap="200">
                  <Text variant="bodyMd" as="p">
                    Configure your FastAPI proxy and Elasticsearch index to enable advanced search.
                  </Text>
                  <Text variant="bodyMd" as="p">
                    Test your connection and index access before enabling the search engine.
                  </Text>
                </BlockStack>
                <Divider />
                <BlockStack gap="200">
                  <InlineStack gap="200" align="start" wrap>
                    <Link url="/app/docs" target="_blank">
                      View Documentation
                    </Link>
                    <Link url="/app/support" target="_blank">
                      Contact Support
                    </Link>
                  </InlineStack>
                </BlockStack>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
