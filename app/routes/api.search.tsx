import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createShopifySearchProxyClient } from "../services/searchProxy.server";
import type { SearchFilters, SearchOptions } from "../services/searchProxy.server";
import { getSearchSettings } from "../services/settings.server";

/**
 * API endpoint for search functionality
 * This can be used by external integrations or for AJAX calls
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  // Authenticate the request
  const { session } = await authenticate.public.appProxy(request);
  
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const body = await request.json();
    const { query, filters = {}, options = {} } = body;
    
    if (!query || typeof query !== "string") {
      return json({ error: "Query parameter is required" }, { status: 400 });
    }
    
    // Validate and sanitize inputs
    const sanitizedFilters: SearchFilters = {
      vendor: Array.isArray(filters.vendor) ? filters.vendor : undefined,
      product_type: Array.isArray(filters.product_type) ? filters.product_type : undefined,
      price: filters.price && typeof filters.price === "object" ? filters.price : undefined,
      is_available: typeof filters.is_available === "boolean" ? filters.is_available : undefined,
      tags: Array.isArray(filters.tags) ? filters.tags : undefined,
    };
    
    const sanitizedOptions: SearchOptions = {
      size: typeof options.size === "number" ? Math.min(100, Math.max(1, options.size)) : 20,
      from: typeof options.from === "number" ? Math.max(0, options.from) : 0,
      sort: typeof options.sort === "string" ? options.sort : "relevance",
    };
    
    // Get settings and check if enabled
    const settings = await getSearchSettings(session.shop);

    if (!settings.isEnabled) {
      return json({
        success: false,
        error: "Search engine is disabled",
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    // Perform search
    const searchProxy = createShopifySearchProxyClient({
      fastApiProxyUrl: settings.fastApiProxyUrl,
      fastApiApiKey: settings.fastApiApiKey,
      websiteKey: settings.websiteKey,
    });

    // Calculate pagination
    const currentPage = Math.floor((sanitizedOptions.from || 0) / (sanitizedOptions.size || 20));
    const pageSize = sanitizedOptions.size || 20;
    const sort = sanitizedOptions.sort || "relevance";

    const results = await searchProxy.search(query, sanitizedFilters, currentPage, pageSize, sort);
    
    return json({
      success: true,
      query,
      results,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error("API search error:", error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Search failed",
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
};

// Handle GET requests with query parameters
export const loader = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.public.appProxy(request);
  
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  
  const url = new URL(request.url);
  const query = url.searchParams.get("q") || url.searchParams.get("query");
  
  if (!query) {
    return json({ error: "Query parameter 'q' or 'query' is required" }, { status: 400 });
  }
  
  try {
    // Parse filters from query parameters
    const filters: SearchFilters = {};
    
    const vendor = url.searchParams.get("vendor");
    if (vendor) {
      filters.vendor = vendor.split(",");
    }
    
    const productType = url.searchParams.get("product_type");
    if (productType) {
      filters.product_type = productType.split(",");
    }
    
    const minPrice = url.searchParams.get("min_price");
    const maxPrice = url.searchParams.get("max_price");
    if (minPrice || maxPrice) {
      filters.price = {
        min: minPrice ? parseFloat(minPrice) : 0,
        max: maxPrice ? parseFloat(maxPrice) : 10000,
      };
    }
    
    const isAvailable = url.searchParams.get("is_available");
    if (isAvailable !== null) {
      filters.is_available = isAvailable === "true";
    }
    
    // Parse options from query parameters
    const options: SearchOptions = {
      size: Math.min(100, Math.max(1, parseInt(url.searchParams.get("size") || "20"))),
      from: Math.max(0, parseInt(url.searchParams.get("from") || "0")),
      sort: url.searchParams.get("sort") || "relevance",
    };
    
    // Get settings and check if enabled
    const settings = await getSearchSettings(session.shop);

    if (!settings.isEnabled) {
      return json({
        success: false,
        error: "Search engine is disabled",
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    // Perform search
    const searchProxy = createShopifySearchProxyClient({
      fastApiProxyUrl: settings.fastApiProxyUrl,
      fastApiApiKey: settings.fastApiApiKey,
      websiteKey: settings.websiteKey,
    });

    // Calculate pagination
    const currentPage = Math.floor((options.from || 0) / (options.size || 20));
    const pageSize = options.size || 20;
    const sort = options.sort || "relevance";

    const results = await searchProxy.search(query, filters, currentPage, pageSize, sort);
    
    return json({
      success: true,
      query,
      results,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error("API search error:", error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Search failed",
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
};
