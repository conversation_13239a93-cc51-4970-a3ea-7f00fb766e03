import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server"; // Adjust path as needed
import { getSearchEngineSettings } from "~/models/settings.server"; // Hypothetical function to fetch from DB

// This loader handles requests to the App Proxy endpoint
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // --- 1. Authenticate the Request (Verify it's from Shopify) ---
  // The authenticate.public.appProxy function is designed for this.
  // It validates the signature added by Shopify to App Proxy requests.
  try {
    const { session } = await authenticate.public.appProxy(request);

    if (!session) {
       console.error("App Proxy authentication failed: No session found.");
       // Shopify expects a 401 or 403 for failed proxy auth, but returning data might be needed for debugging.
       // Often, returning an empty object or a specific error message is done.
       return json({ error: "Authentication failed" }, { status: 401 });
    }

    const shop = session.shop; // Get the shop domain
    console.log(`[App Proxy] Authenticated request for shop: ${shop}`);

    // --- 2. Fetch Settings from Your Database ---
    // You need a function to retrieve the settings based on the shop domain.
    // This assumes you have a data model/service for settings.
    const settings = await getSearchEngineSettings(shop);

    if (!settings) {
        console.warn(`[App Proxy] No settings found for shop: ${shop}`);
        // Return default settings or an empty object if preferred
        // Returning an object with enable_search_engine: false is often clearer
        return json({ enable_search_engine: false });
    }

    // --- 3. Return Settings as JSON ---
    // Map your internal settings object to the structure expected by your script.js
    const responseSettings = {
        fastapi_proxy_url: settings.fastapiProxyUrl || "", // Adjust property names if needed
        api_key: settings.apiKey || "",
        website_key: settings.websiteKey || "",
        elasticsearch_index_name: settings.elasticsearchIndexName || "",
        enable_search_engine: settings.enableSearchEngine ?? false, // Use nullish coalescing
    };

    console.log(`[App Proxy] Returning settings for ${shop}:`, responseSettings);
    return json(responseSettings); // Remix's json helper sets Content-Type correctly

  } catch (error) {
    // Handle authentication errors or database errors
    console.error("[App Proxy] Error processing request:", error);
    // It's often better to return a 200 with an error object for App Proxies
    // to avoid generic Shopify error pages, but log the real error server-side.
    return json({ error: "Failed to load settings", enable_search_engine: false }, { status: 200 });
    // Or, if you prefer strict error codes:
    // return json({ error: "Internal Server Error" }, { status: 500 });
  }
};