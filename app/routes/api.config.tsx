import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getSearchSettings } from "../services/settings.server";

/**
 * API endpoint to provide search configuration to the frontend
 * This can be called by the theme to get current search settings
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const shop = url.searchParams.get("shop");
    
    if (!shop) {
      return json({ error: "Shop parameter is required" }, { status: 400 });
    }

    // Get settings from database
    const settings = await getSearchSettings(shop);

    // Return configuration for frontend
    const config = {
      apiEndpoint: "/api/search",
      resultsPerPage: 20,
      enableAutocomplete: true,
      enableBarcodeSearch: true,
      defaultSort: "relevance",
      shopDomain: shop,
      currency: "USD",
      // eslint-disable-next-line no-template-curly-in-string
      moneyFormat: "${{amount}}",
      isEnabled: settings.isEnabled
    };

    return json(config, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
        "Cache-Control": "public, max-age=300" // Cache for 5 minutes
      }
    });

  } catch (error) {
    console.error("Config API error:", error);
    
    return json({
      error: "Failed to load configuration",
      // Fallback config
      apiEndpoint: "/api/search",
      resultsPerPage: 20,
      enableAutocomplete: true,
      enableBarcodeSearch: true,
      defaultSort: "relevance",
      shopDomain: "",
      currency: "USD",
      // eslint-disable-next-line no-template-curly-in-string
      moneyFormat: "${{amount}}",
      isEnabled: false
    }, { status: 500 });
  }
};
