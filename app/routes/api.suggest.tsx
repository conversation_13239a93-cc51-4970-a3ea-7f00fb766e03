import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createSearchProxyClient } from "../services/searchProxy.server";
import { getSearchSettings } from "../services/settings.server";

/**
 * API endpoint for search suggestions/autocomplete
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.public.appProxy(request);
  
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const body = await request.json();
    const { query, size = 10 } = body;

    const settings = await getSearchSettings(session.shop);
    
    if (!query || typeof query !== "string") {
      return json({ error: "Query parameter is required" }, { status: 400 });
    }
    
    if (query.length < 2) {
      return json({
        success: true,
        query,
        suggestions: [],
        timestamp: new Date().toISOString(),
      });
    }
    
    // Validate size parameter
    const validSize = Math.min(20, Math.max(1, typeof size === "number" ? size : 10));
    
    // Get suggestions from search proxy
    const searchProxy = createSearchProxyClient();
    const results = await searchProxy.suggest(query, validSize);
    
    return json({
      success: true,
      query,
      suggestions: results.suggestions,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error("API suggest error:", error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Suggestions failed",
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
};

// Handle GET requests for suggestions
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.public.appProxy(request);
  
  const url = new URL(request.url);
  const query = url.searchParams.get("q") || url.searchParams.get("query");
  
  if (!query) {
    return json({ error: "Query parameter 'q' or 'query' is required" }, { status: 400 });
  }
  
  if (query.length < 2) {
    return json({
      success: true,
      query,
      suggestions: [],
      timestamp: new Date().toISOString(),
    });
  }
  
  try {
    const size = Math.min(20, Math.max(1, parseInt(url.searchParams.get("size") || "10")));
    
    // Get suggestions from search proxy
    const searchProxy = createSearchProxyClient();
    const results = await searchProxy.suggest(query, size);
    
    return json({
      success: true,
      query,
      suggestions: results.suggestions,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error("API suggest error:", error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Suggestions failed",
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
};
