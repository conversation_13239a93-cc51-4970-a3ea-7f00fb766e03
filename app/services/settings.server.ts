import { PrismaClient } from "@prisma/client";
import { syncSettingsToMetafields } from "./metafields.server";

const prisma = new PrismaClient();

export interface Settings {
  id: string;
  shop: string;
  fastApiProxyUrl: string;
  fastApiApiKey: string | null;
  websiteKey: string;
  elasticsearchIndex: string;
  isEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdateSettingsData {
  fastApiProxyUrl?: string;
  fastApiApiKey?: string;
  websiteKey?: string;
  elasticsearchIndex?: string;
  isEnabled?: boolean;
}

/**
 * Get search settings for a specific shop
 * Creates default settings if none exist
 */
export async function getSettings(shop: string): Promise<Settings> {
  let settings = await prisma.searchSettings.findUnique({
    where: { shop }
  });

  // Create default settings if none exist
  if (!settings) {
    settings = await prisma.searchSettings.create({
      data: {
        shop,
        fastApiProxyUrl: process.env.FASTAPI_PROXY_URL || "http://localhost:8000",
        fastApiApiKey: process.env.FASTAPI_API_KEY || null,
        websiteKey: process.env.WEBSITE_KEY || "shopify_products",
        elasticsearchIndex: process.env.ELASTICSEARCH_INDEX_NAME || "shopify_products",
        isEnabled: true,
      }
    });

    // Sync initial settings to Shopify metafields
    await syncSettingsToMetafields(shop, settings);
  }

  return settings;
}

/**
 * Update search settings for a specific shop
 */
export async function updateSettings(
  shop: string, 
  data: UpdateSettingsData
): Promise<Settings> {
  // Ensure settings exist first
  await getSearchSettings(shop);

  const updatedSettings = await prisma.searchSettings.update({
    where: { shop },
    data: {
      ...data,
      updatedAt: new Date(),
    }
  });

  // Sync settings to Shopify metafields for frontend access
  await syncSettingsToMetafields(shop, updatedSettings);

  return updatedSettings;
}

/**
 * Test connection to Shopify FastAPI service with given settings
 */
export async function testFastApiConnection(
  fastApiProxyUrl: string,
  fastApiApiKey: string | null
): Promise<{ success: boolean; error?: string; responseTime?: number }> {
  try {
    const startTime = Date.now();

    const response = await fetch(`${fastApiProxyUrl}/shopify/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...(fastApiApiKey && { "X-API-Key": fastApiApiKey }),
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime,
      };
    }

    return {
      success: true,
      responseTime,
    };
  } catch (error) {
    console.log(error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Connection failed",
    };
  }
}

/**
 * Test Shopify Elasticsearch index accessibility
 */
export async function testElasticsearchIndex(
  fastApiProxyUrl: string,
  fastApiApiKey: string | null,
  websiteKey: string,
  elasticsearchIndex: string
): Promise<{ success: boolean; error?: string; documentCount?: number }> {
  try {
    const response = await fetch(`${fastApiProxyUrl}/shopify/index/status`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(fastApiApiKey && { "X-API-Key": fastApiApiKey }),
      },
      body: JSON.stringify({
        index_name: elasticsearchIndex,
        website_key: websiteKey,
      }),
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const data = await response.json();

    return {
      success: data.success || false,
      documentCount: data.document_count || 0,
    };
  } catch (error) {
    console.log(error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Index check failed",
    };
  }
}

/**
 * Get all shops with search settings (for admin purposes)
 */
export async function getAllSettings(): Promise<Settings[]> {
  return await prisma.searchSettings.findMany({
    orderBy: { updatedAt: 'desc' }
  });
}

/**
 * Delete search settings for a shop (cleanup when app is uninstalled)
 */
export async function deleteSettings(shop: string): Promise<void> {
  await prisma.searchSettings.delete({
    where: { shop }
  }).catch(() => {
    // Ignore errors if settings don't exist
  });
}

/**
 * Validate settings data
 */
export function validateSettings(data: UpdateSettingsData): { 
  isValid: boolean; 
  errors: string[] 
} {
  const errors: string[] = [];

  if (data.fastApiProxyUrl !== undefined) {
    try {
      new URL(data.fastApiProxyUrl);
    } catch {
      errors.push("FastAPI Proxy URL must be a valid URL");
    }
  }

  if (data.websiteKey !== undefined) {
    if (!data.websiteKey || data.websiteKey.trim().length === 0) {
      errors.push("Website Key cannot be empty");
    }
    if (data.websiteKey && !/^[a-zA-Z0-9_-]+$/.test(data.websiteKey)) {
      errors.push("Website Key can only contain letters, numbers, underscores, and hyphens");
    }
  }

  if (data.elasticsearchIndex !== undefined) {
    if (!data.elasticsearchIndex || data.elasticsearchIndex.trim().length === 0) {
      errors.push("Elasticsearch Index cannot be empty");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Cleanup function to close Prisma connection
export async function cleanup() {
  await prisma.$disconnect();
}
