/**
 * Shopify Search Proxy Service
 *
 * Handles communication with the Shopify FastAPI service for product variant search.
 * This service provides a clean interface for the Shopify app to interact with
 * the Shopify-specific FastAPI endpoints.
 */

export interface SearchFilters {
  vendor?: string[];
  product_type?: string[];
  price?: { min: number; max: number };
  is_available?: boolean;
  status?: string[];
  tags?: string[];
  inventory?: { min: number; max: number };
}

export interface SearchOptions {
  size?: number;
  from?: number;
  sort?: string;
}

// Based on actual Shopify feed structure
export interface ProductVariant {
  // Product fields
  product_id: string;
  title: string;
  description: string;
  vendor: string;
  product_type: string;
  tags: string;
  url: string;
  primary_image: string;
  all_images: string;
  created_at: string;
  status: string;

  // Variant fields
  variant_id: string;
  sku: string;
  barcode: string;
  price: number;
  inventory: number;
  is_available: boolean;
  compare_at_price?: number;

  // Search metadata
  score: number;
}

// Response format from Shopify FastAPI service
export interface SearchResponse {
  data: ProductVariant[];
  totalProducts: number;
  filterElements: {
    minPrice?: number;
    maxPrice?: number;
    vendors: string[];
    productTypes: string[];
    statuses: string[];
    availability: { [key: string]: number };
  };
  took: number;
  maxScore?: number;
}

export class ShopifySearchProxyClient {
  private baseUrl: string;
  private apiKey: string;
  private websiteKey: string;
  private timeout: number;

  constructor(config: { baseUrl: string; apiKey: string; websiteKey: string; timeout?: number }) {
    this.baseUrl = config.baseUrl;
    this.apiKey = config.apiKey;
    this.websiteKey = config.websiteKey;
    this.timeout = config.timeout || 5000;
  }

  /**
   * Search Shopify product variants via FastAPI service
   */
  async search(
    pattern: string,
    filters: SearchFilters = {},
    currentPage: number = 0,
    pageSize: number = 20,
    sort: string = "relevance"
  ): Promise<SearchResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/shopify/search`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(this.apiKey && { "X-API-Key": this.apiKey }),
        },
        body: JSON.stringify({
          index: this.websiteKey, // Use website key as index name
          pattern: this.sanitizeQuery(pattern),
          current_page: currentPage,
          page_size: pageSize,
          args: this.sanitizeFilters(filters, sort),
        }),
        signal: AbortSignal.timeout(this.timeout),
      });

      if (!response.ok) {
        throw new Error(`Search request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Shopify search proxy error:", error);
      if (error instanceof Error) {
        throw new Error(`Search service error: ${error.message}`);
      }
      throw new Error("Search service temporarily unavailable");
    }
  }

  /**
   * Test connection to the Shopify FastAPI service
   */
  async testConnection(): Promise<{ success: boolean; error?: string; responseTime?: number }> {
    try {
      const startTime = Date.now();

      const response = await fetch(`${this.baseUrl}/shopify/health`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          ...(this.apiKey && { "X-API-Key": this.apiKey }),
        },
        signal: AbortSignal.timeout(this.timeout),
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
          responseTime,
        };
      }

      return {
        success: true,
        responseTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Connection failed",
      };
    }
  }

  /**
   * Sanitize search query to prevent injection attacks
   */
  private sanitizeQuery(query: string): string {
    if (!query || typeof query !== "string") {
      return "";
    }

    // Remove HTML tags
    let sanitized = query.replace(/<[^>]+>/g, "");

    // Remove potentially dangerous characters
    sanitized = sanitized.replace(/[<>";\\]/g, "");

    // Limit length
    sanitized = sanitized.substring(0, 200);

    return sanitized.trim();
  }

  /**
   * Sanitize filter values and combine with sort option
   */
  private sanitizeFilters(filters: SearchFilters, sort?: string): any {
    const args: any = {};

    // Add filters
    if (filters.vendor && Array.isArray(filters.vendor)) {
      args.vendor = filters.vendor
        .filter(v => typeof v === "string")
        .map(v => v.substring(0, 100))
        .slice(0, 10);
    }

    if (filters.product_type && Array.isArray(filters.product_type)) {
      args.product_type = filters.product_type
        .filter(v => typeof v === "string")
        .map(v => v.substring(0, 100))
        .slice(0, 10);
    }

    if (filters.price && typeof filters.price === "object") {
      const { min, max } = filters.price;
      if (typeof min === "number" && typeof max === "number" && min >= 0 && max >= min) {
        args.price = { min: Math.max(0, min), max: Math.min(10000, max) };
      }
    }

    if (typeof filters.is_available === "boolean") {
      args.is_available = filters.is_available;
    }

    if (filters.status && Array.isArray(filters.status)) {
      args.status = filters.status
        .filter(v => typeof v === "string")
        .slice(0, 5);
    }

    if (filters.tags && Array.isArray(filters.tags)) {
      args.tags = filters.tags
        .filter(v => typeof v === "string")
        .map(v => v.substring(0, 50))
        .slice(0, 20);
    }

    if (filters.inventory && typeof filters.inventory === "object") {
      const { min, max } = filters.inventory;
      if (typeof min === "number" && typeof max === "number" && min >= 0 && max >= min) {
        args.inventory = { min: Math.max(0, min), max: Math.min(100000, max) };
      }
    }

    // Add sort option
    if (sort) {
      args.sort = sort;
    }

    return args;
  }
}

/**
 * Create a Shopify search proxy client instance with settings
 */
export function createShopifySearchProxyClient(settings: {
  fastApiProxyUrl: string;
  fastApiApiKey: string | null;
  websiteKey: string;
}): ShopifySearchProxyClient {
  return new ShopifySearchProxyClient({
    baseUrl: settings.fastApiProxyUrl,
    apiKey: settings.fastApiApiKey || "",
    websiteKey: settings.websiteKey,
    timeout: 5000,
  });
}
