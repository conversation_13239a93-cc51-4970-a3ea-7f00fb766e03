// extensions/search-engine/assets/script.js

(function () {
  let debounceTimer;

  // --- YOUR CUSTOM SEARCH LOGIC FUNCTIONS ---
  async function fetchSuggestions(query) {
    console.log(`[Custom Search] Fetching suggestions for: "${query}"`);
    // --- Replace with your actual API call ---
    // Example mock response - Replace this!
    // await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay
    // const mockSuggestions = [
    //   { title: `Suggestion 1 for ${query}`, url: `/products/item1` },
    //   { title: `Suggestion 2 for ${query}`, url: `/collections/all` },
    // ];
    // return mockSuggestions;
    return []; // Placeholder
  }

  // --- NEW/UPDATED: Manage both custom and default suggestions ---
  function hideDefaultSuggestions() {
    // --- CRITICAL: Identify and hide/remove Shopify's default suggestion elements ---
    // You MUST inspect your storefront's HTML to find the correct selectors.
    // Common patterns for Shopify's suggestion containers:
    // - They are often <div>, <ul>, or <div role="listbox"> elements.
    // - They are siblings or descendants of the search input/form.
    // - They might have classes like 'predictive-search', 'search__results', 'search-results', etc.
    // - They are usually positioned absolutely or fixed below the input.

    // Example selectors (YOU NEED TO ADJUST THESE BASED ON YOUR THEME'S HTML):
    const defaultSuggestionSelectors = [
      '.predictive-search', // Common class used by Dawn and others
      '[data-predictive-search]', // Another common attribute
      '.search__results',    // Another potential class
      '.search-results',     // Another potential class
      'ul.search-suggestions', // Example structure
      'div[role="listbox"]'  // Generic ARIA role sometimes used
      // Add more selectors specific to your theme if needed
    ];

    defaultSuggestionSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // Option 1: Hide the element (safer if it's reused)
        element.style.display = 'none';
        element.style.visibility = 'hidden';
        // Option 2: Remove the element from the DOM (riskier)
        // element.remove();
        console.log(`[Custom Search] Attempted to hide default suggestions using selector: ${selector}`, element);
      });
    });
  }

  function showCustomSuggestions(suggestions) {
    // --- YOUR LOGIC TO DISPLAY YOUR CUSTOM SUGGESTIONS ---
    console.log('[Custom Search] Displaying custom suggestions:', suggestions);

    // Example: Find YOUR custom suggestions container (you need to create this in your HTML/Liquid or dynamically)
    // const customSuggestionsContainer = document.getElementById('my-custom-suggestions-container');
    // if (customSuggestionsContainer) {
    //   customSuggestionsContainer.innerHTML = ''; // Clear previous
    //   if (suggestions && suggestions.length > 0) {
    //     const listHtml = suggestions.map(s => `<div class="custom-suggestion-item"><a href="${s.url}">${s.title}</a></div>`).join('');
    //     customSuggestionsContainer.innerHTML = listHtml;
    //     customSuggestionsContainer.style.display = 'block'; // Show it
    //   } else {
    //      customSuggestionsContainer.style.display = 'none'; // Hide if no suggestions
    //   }
    // }

    // Placeholder UI for demonstration
    if (suggestions && suggestions.length > 0) {
       alert(`Showing Custom Suggestion: ${suggestions[0].title}`); // Replace with actual UI update
    }

    // --- CRITICAL: Ensure default suggestions are hidden when showing custom ones ---
    hideDefaultSuggestions();
  }

  function clearAllSuggestions() {
    // --- CLEAR YOUR CUSTOM SUGGESTIONS ---
    // Example:
    // const customSuggestionsContainer = document.getElementById('my-custom-suggestions-container');
    // if (customSuggestionsContainer) {
    //   customSuggestionsContainer.innerHTML = '';
    //   customSuggestionsContainer.style.display = 'none'; // Hide it
    // }
    console.log('[Custom Search] Clearing custom suggestions.');

    // --- CRITICAL: Reveal default suggestions if needed (optional, or just ensure they stay hidden) ---
    // Usually, it's better/safer to just keep hiding them.
    // However, if you temporarily hid them with display:none, you *could* revert it IF you are sure
    // no other logic depends on them being hidden.
    // For simplicity and robustness, we'll just re-hide them or ensure they remain hidden.
    hideDefaultSuggestions(); // Ensure they are definitely hidden
  }
  // --- END CUSTOM LOGIC FUNCTIONS ---


  function initializeCustomSearch() {
    const searchForm = document.querySelector('form[action*="/search"]');
    if (!searchForm) {
      console.log('[Custom Search] Search form not found.');
      return;
    }
    console.log('[Custom Search] Found search form.');

    const searchInput = searchForm.querySelector('input[type="search"]') ||
                        searchForm.querySelector('input[name="q"]');
    if (!searchInput) {
      console.log('[Custom Search] Search input not found.');
      return;
    }
    console.log('[Custom Search] Found search input.');

    // --- HANDLE FORM SUBMISSION (as before) ---
    searchForm.addEventListener('submit', function(event) {
      event.preventDefault();
      const query = searchInput.value.trim();
      if (!query) {
        alert('Please enter a search term.');
        return;
      }
      console.log('[Custom Search] Submit intercepted:', query);
      alert(`Custom search submitted for: "${query}"`); // Replace with actual submit logic
    });
    // --- END FORM SUBMISSION ---


    // --- HANDLE INPUT TYPING ---
    searchInput.addEventListener('input', function(event) {
      const query = event.target.value.trim();
      clearTimeout(debounceTimer);

      // CRITICAL: Hide default suggestions on every input event
      // This helps ensure they don't pop up unexpectedly even before your debounce delay.
      hideDefaultSuggestions();

      if (query.length === 0) {
        clearAllSuggestions();
        return;
      }

      debounceTimer = setTimeout(async function() {
        console.log(`[Custom Search] Input paused. Query: "${query}"`);
        const suggestions = await fetchSuggestions(query);
        showCustomSuggestions(suggestions);
      }, 300);

    });

    // --- HANDLE BLUR/CLICK TO CLEAR ---
    searchInput.addEventListener('blur', function() {
       setTimeout(clearAllSuggestions, 200); // Delay to allow clicking suggestions
    });

    document.addEventListener('click', function(event) {
       if (!searchForm.contains(event.target)) {
           clearAllSuggestions();
       }
    });
    // --- END INPUT HANDLING ---

    // --- CRITICAL: Initial hide attempt ---
    // Try to hide any default suggestions that might already be present on page load
    // or attached by Shopify's script immediately.
    hideDefaultSuggestions();
    console.log('[Custom Search] Initial attempt to hide default suggestions made.');
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCustomSearch);
  } else {
    initializeCustomSearch();
  }
})();
